import requests

access_token = "******************************************************************************************************************************************************************************************************************************"





def get_google_business_accounts(access_token):
    url = 'https://mybusinessbusinessinformation.googleapis.com/v1/accounts'
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = requests.get(url, headers=headers)
    print('--------->',response.json())
    return response.json()


def get_google_business_locations(access_token, account_name):
    # account_name example: 'accounts/**********'
    url = f'https://mybusinessbusinessinformation.googleapis.com/v1/{account_name}/locations'
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = requests.get(url, headers=headers)
    print('-----2-------->',response.json())
    return response.json()

def get_google_business_reviews(access_token, location_name):
    # location_name example: 'accounts/**********/locations/**********'
    url = f'https://mybusiness.googleapis.com/v4/{location_name}/reviews'
    headers = {
        'Authorization': f'Bearer {access_token}'
    }
    response = requests.get(url, headers=headers)
    print('------3---------->',response.json())
    return response.json()


# Step 1: Get Accounts
accounts = get_google_business_accounts(access_token)
print(accounts)

# account_name = accounts['accounts'][0]['name']  # e.g., 'accounts/**********'

# # Step 2: Get Locations
# locations = get_google_business_locations(access_token, account_name)
# location_name = locations['locations'][0]['name']  # e.g., 'accounts/**********/locations/**********'

# # Step 3: Get Reviews
# reviews = get_google_business_reviews(access_token, location_name)

# print(reviews)


