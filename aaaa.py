import requests

def get_gbp_location_names(access_token):
    headers = {
        "Authorization": f"Bearer {access_token}"
    }

    # Step 1: Get account ID
    accounts_url = "https://mybusinessbusinessinformation.googleapis.com/v1/accounts"
    accounts_resp = requests.get(accounts_url, headers=headers).json()
    print(accounts_resp)
    accounts = accounts_resp.get("accounts", [])
    if not accounts:
        print("❌ No Google Business accounts found.")
        return []

    account_name = accounts[0]["name"]  # e.g., "accounts/**********"

    # Step 2: Get locations under this account
    locations_url = f"https://mybusinessbusinessinformation.googleapis.com/v1/{account_name}/locations"
    locations_resp = requests.get(locations_url, headers=headers).json()

    locations = locations_resp.get("locations", [])
    if not locations:
        print("❌ No business locations found.")
        return []

    # Extract and return location names
    location_names = [location["name"] for location in locations]
    return location_names


access_token = "******************************************************************************************************************************************************************************************************************************"
location_names = get_gbp_location_names(access_token)
print(location_names)